import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Routes, Route, Navigate } from "react-router-dom";

import Welcome from "./pages/Welcome";
import Dashboard from "./pages/Dashboard"; 
import NotFound from "./pages/NotFound";
import Loans from "./pages/Loans";
import Market from "./pages/Market";
import ProductCategory from "./pages/ProductCategory";
import ProductDetail from "./pages/ProductDetail";
import Checkout from "./pages/Checkout";
import Labour from "./pages/Labour";
import Machinery from "./pages/Machinery";
import Export from "./pages/Export";
import Monitoring from "./pages/Monitoring";
import Services from "./pages/Services";
import Contact from "./pages/Contact";
import FarmingType from "./pages/FarmingType"; 
import CropAllocation from "./pages/CropAllocation";
import Orders from "./pages/Orders";
import Settings from "./pages/Settings";
import ExecutiveDashboard from "./pages/ExecutiveDashboard";
import ExecutiveFarmers from "./pages/ExecutiveFarmers";
import ExecutiveAnalytics from "./pages/ExecutiveAnalytics";
import ExecutiveFinancial from "./pages/ExecutiveFinancial";
import ExecutiveOperations from "./pages/ExecutiveOperations";
import ExecutiveCommunications from "./pages/ExecutiveCommunications";

const App = () => (
  <TooltipProvider>
    <Toaster />
    <Routes>
              <Route path="/" element={<Welcome />} />
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/executive-dashboard" element={<ExecutiveDashboard />} />
              <Route path="/executive/farmers" element={<ExecutiveFarmers />} />
              <Route path="/executive/analytics" element={<ExecutiveAnalytics />} />
              <Route path="/executive/financial" element={<ExecutiveFinancial />} />
              <Route path="/executive/operations" element={<ExecutiveOperations />} />
              <Route path="/executive/communications" element={<ExecutiveCommunications />} />
              <Route path="/farming-type" element={<FarmingType />} />
              <Route path="/crop-allocation" element={<CropAllocation />} />
              <Route path="/loans" element={<Loans />} />
              <Route path="/market" element={<Market />} />
              <Route path="/market/:categoryId" element={<ProductCategory />} />
              <Route path="/market/:categoryId/:productId" element={<ProductDetail />} />
              <Route path="/checkout" element={<Checkout />} />
              <Route path="/labour" element={<Labour />} />
              <Route path="/machinery" element={<Machinery />} />
              <Route path="/export" element={<Export />} />
              <Route path="/monitoring" element={<Monitoring />} />
              <Route path="/services" element={<Services />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/orders" element={<Orders />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="*" element={<NotFound />} />
    </Routes>
  </TooltipProvider>
);

export default App;
