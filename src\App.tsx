function App() {
  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      backgroundColor: '#f0f0f0',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{
        color: '#333',
        fontSize: '32px',
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        🌱 Agri-Lift Soil Insight
      </h1>

      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '10px',
        boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
        maxWidth: '500px',
        textAlign: 'center'
      }}>
        <h2 style={{ color: '#4CAF50', marginBottom: '15px' }}>
          ✅ Application is Working!
        </h2>

        <p style={{ color: '#666', marginBottom: '15px' }}>
          React is successfully rendering. The blank screen issue has been resolved.
        </p>

        <div style={{
          backgroundColor: '#e8f5e8',
          padding: '10px',
          borderRadius: '5px',
          marginBottom: '15px'
        }}>
          <strong>Current Status:</strong><br/>
          Frontend: http://localhost:8083<br/>
          Backend: http://localhost:5006<br/>
          Database: Connected ✅
        </div>

        <button
          onClick={() => {
            window.location.reload();
          }}
          style={{
            backgroundColor: '#4CAF50',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '5px',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
}

export default App;
