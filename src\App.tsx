import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Routes, Route, Navigate } from "react-router-dom";

import Welcome from "./pages/Welcome";
import Dashboard from "./pages/Dashboard"; 
import NotFound from "./pages/NotFound";
import Loans from "./pages/Loans";
import Market from "./pages/Market";
import ProductCategory from "./pages/ProductCategory";
import ProductDetail from "./pages/ProductDetail";
import Checkout from "./pages/Checkout";
import Labour from "./pages/Labour";
import Machinery from "./pages/Machinery";
import Export from "./pages/Export";
import Monitoring from "./pages/Monitoring";
import Services from "./pages/Services";
import Contact from "./pages/Contact";
import FarmingType from "./pages/FarmingType"; 
import CropAllocation from "./pages/CropAllocation";
import Orders from "./pages/Orders";
import Settings from "./pages/Settings";
import ExecutiveDashboard from "./pages/ExecutiveDashboard";
import ExecutiveFarmers from "./pages/ExecutiveFarmers";
import ExecutiveAnalytics from "./pages/ExecutiveAnalytics";
import ExecutiveFinancial from "./pages/ExecutiveFinancial";
import ExecutiveOperations from "./pages/ExecutiveOperations";
import ExecutiveCommunications from "./pages/ExecutiveCommunications";

const App = () => {
  console.log('App component is rendering');

  return (
    <div style={{ padding: '20px', background: 'white', minHeight: '100vh' }}>
      <h1 style={{ color: 'black', fontSize: '24px', marginBottom: '20px' }}>
        🌱 Agri-Lift Soil Insight - Debug Mode
      </h1>
      <div style={{ color: 'green', fontSize: '18px', marginBottom: '10px' }}>
        ✅ React is working!
      </div>
      <div style={{ color: 'blue', fontSize: '16px', marginBottom: '10px' }}>
        Frontend URL: {window.location.href}
      </div>
      <div style={{ color: 'purple', fontSize: '16px', marginBottom: '20px' }}>
        Backend API: http://localhost:5005/api
      </div>

      <button
        onClick={() => {
          fetch('http://localhost:5005/health')
            .then(r => r.json())
            .then(d => alert('Backend connected: ' + JSON.stringify(d)))
            .catch(e => alert('Backend error: ' + e.message));
        }}
        style={{
          padding: '10px 20px',
          background: '#4CAF50',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: 'pointer',
          fontSize: '16px'
        }}
      >
        Test Backend Connection
      </button>

      <div style={{ marginTop: '20px', padding: '10px', background: '#f0f0f0', borderRadius: '5px' }}>
        <h3>Debug Info:</h3>
        <p>User Agent: {navigator.userAgent}</p>
        <p>Screen: {window.screen.width}x{window.screen.height}</p>
        <p>Viewport: {window.innerWidth}x{window.innerHeight}</p>
      </div>
    </div>
  );
};

export default App;
