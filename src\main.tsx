
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import App from "./App.tsx";
import "./index.css";
import { LanguageProvider } from "./components/LanguageContext";
import { CartProvider } from "./context/CartContext";
import { ThemeProvider } from "./contexts/ThemeContext";
import { WishlistProvider } from "./contexts/WishlistContext";
import { AuthProvider } from "./contexts/AuthContext";
import { Toaster } from "@/components/ui/sonner";

const queryClient = new QueryClient();

console.log('main.tsx is loading...');

const rootElement = document.getElementById("root");
console.log('Root element:', rootElement);

if (rootElement) {
  createRoot(rootElement).render(
    <StrictMode>
      <div>
        <App />
      </div>
    </StrictMode>
  );
  console.log('React app rendered');
} else {
  console.error('Root element not found!');
}
