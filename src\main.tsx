
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.tsx";

console.log('Starting React app...');

const rootElement = document.getElementById("root");
console.log('Root element found:', !!rootElement);

if (rootElement) {
  try {
    createRoot(rootElement).render(
      <StrictMode>
        <App />
      </StrictMode>
    );
    console.log('React app rendered successfully');
  } catch (error) {
    console.error('Error rendering React app:', error);
    document.body.innerHTML = `
      <div style="padding: 20px; font-family: Arial;">
        <h1 style="color: red;">React Rendering Error</h1>
        <p>Error: ${error}</p>
        <p>Check the browser console for more details.</p>
      </div>
    `;
  }
} else {
  console.error('Root element not found!');
  document.body.innerHTML = `
    <div style="padding: 20px; font-family: Arial;">
      <h1 style="color: red;">Root Element Missing</h1>
      <p>Could not find element with id="root"</p>
    </div>
  `;
}
