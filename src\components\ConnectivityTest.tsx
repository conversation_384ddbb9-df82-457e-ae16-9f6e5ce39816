import { useState, useEffect } from 'react';

const ConnectivityTest = () => {
  const [status, setStatus] = useState('Testing...');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnectivity = async () => {
      try {
        console.log('Testing backend connectivity...');
        const response = await fetch('http://localhost:5005/health');
        
        if (response.ok) {
          const data = await response.json();
          setStatus(`✅ Backend connected: ${data.status}`);
          console.log('Backend response:', data);
        } else {
          setStatus(`❌ Backend error: ${response.status}`);
          setError(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (err) {
        console.error('Connectivity test failed:', err);
        setStatus('❌ Backend unreachable');
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    testConnectivity();
  }, []);

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      background: 'white', 
      padding: '10px', 
      border: '1px solid #ccc',
      borderRadius: '5px',
      zIndex: 9999,
      fontSize: '12px',
      maxWidth: '300px'
    }}>
      <div><strong>Backend Status:</strong> {status}</div>
      {error && <div style={{ color: 'red', marginTop: '5px' }}><strong>Error:</strong> {error}</div>}
    </div>
  );
};

export default ConnectivityTest;
